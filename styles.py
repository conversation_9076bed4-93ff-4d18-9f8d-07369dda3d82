"""
ملف التنسيقات العامة للتطبيق
يحتوي على كل الأنماط المستخدمة في مختلف أجزاء التطبيق
"""
import sys
from PyQt5.QtGui import QFontDatabase, QFont

class AppStyles:
    """
    فئة تحتوي على كل التنسيقات المستخدمة في التطبيق
    """

    # الخط المستخدم في التطبيق - سيتم تحديثه عند التهيئة
    FONT_FAMILY = "Arial"

    # متغير لتخزين اسم الخط المخصص المحمل من ملف TTF
    LOADED_CUSTOM_FONT = None

    @classmethod
    def initialize_fonts(cls):
        # Inicializar fuentes según disponibilidad en el sistema
        # التحقق أولاً إذا تم تحميل الخط المخصص من ملف TTF
        if hasattr(cls, 'LOADED_CUSTOM_FONT') and cls.LOADED_CUSTOM_FONT:
            cls.FONT_FAMILY = cls.LOADED_CUSTOM_FONT
            print(f"استخدام الخط المحمل من الملف: {cls.FONT_FAMILY}")
            return cls.FONT_FAMILY

        # Intenta detectar ReadexPro directamente
        readex_font = cls._find_system_font("Readex")
        if readex_font:
            cls.FONT_FAMILY = readex_font
            print(f"تم تعيين الخط من النظام: {cls.FONT_FAMILY}")
            return cls.FONT_FAMILY

        # Lista de fuentes preferidas para árabe
        # قائمة الخطوط المفضلة بترتيب الأفضلية للغة العربية
        preferred_fonts = [
            "Readex Pro",      # خط عربي عصري ومناسب للواجهات
            "Tahoma",          # خط مناسب للغة العربية
            "Times New Roman", # خط مناسب للغة العربية
            "Arial",           # خط بديل متوفر في معظم الأنظمة
            "Segoe UI",        # خط Windows 10/8/7
            "Droid Arabic Naskh",  # خط عربي
            "Noto Sans Arabic",    # خط عربي من Google
            "Dubai",              # خط عربي من Microsoft
            "Almarai",           # خط عربي
            "Sans Serif"        # خط افتراضي
        ]

        # الحصول على قائمة الخطوط المتوفرة في النظام
        available_fonts = QFontDatabase().families()

        # البحث عن أول خط متوفر من قائمة الخطوط المفضلة
        for font_name in preferred_fonts:
            for system_font in available_fonts:
                if font_name.lower() in system_font.lower():
                    cls.FONT_FAMILY = system_font
                    print(f"تم تعيين الخط: {cls.FONT_FAMILY}")
                    return cls.FONT_FAMILY

        # إذا لم يتم العثور على أي خط، استخدم الخط الافتراضي
        print("لم يتم العثور على أي خط مناسب، استخدام الخط الافتراضي")
        return cls.FONT_FAMILY

    @classmethod
    def _find_system_font(cls, font_partial_name):
        # Buscar fuente en el sistema por nombre parcial
        # البحث عن خط في النظام يحتوي على الاسم الجزئي المحدد
        available_fonts = QFontDatabase().families()
        for font in available_fonts:
            if font_partial_name.lower() in font.lower():
                return font
        return None

        # قائمة الخطوط المفضلة بترتيب الأفضلية
        preferred_fonts = [
            "Readex Pro",          # خط عربي عصري ومناسب للواجهات
            "Tahoma",              # خط مناسب للغة العربية
            "Arial",               # خط بديل متوفر في معظم الأنظمة
            "Segoe UI Variable",   # خط Windows 11 الجديد
            "Segoe UI Light",      # خط Segoe UI الخفيف
            "Segoe UI",            # خط Windows 10/8/7
            "Sans Serif"           # خط افتراضي
        ]

        # الحصول على قائمة الخطوط المتوفرة في النظام
        available_fonts = QFontDatabase().families()

        # طباعة قائمة الخطوط للتشخيص
        print("الخطوط المتاحة في النظام:")
        arabic_fonts = [f for f in available_fonts if "readex" in f.lower() or "tahoma" in f.lower() or "arial" in f.lower() or "segoe" in f.lower()]
        for font in arabic_fonts:
            print(f"- {font}")

        # البحث عن Readex Pro مباشرة
        readex_matches = [
            f for f in available_fonts
            if "readex pro" in f.lower() or "readexpro" in f.lower()
        ]

        if readex_matches:
            cls.FONT_FAMILY = readex_matches[0]
            print(f"تم تعيين الخط: {cls.FONT_FAMILY}")
            return cls.FONT_FAMILY

        # البحث عن Tahoma كبديل
        tahoma_matches = [
            f for f in available_fonts
            if "tahoma" in f.lower()
        ]

        if tahoma_matches:
            cls.FONT_FAMILY = tahoma_matches[0]
            print(f"تم تعيين الخط: {cls.FONT_FAMILY}")
            return cls.FONT_FAMILY

        # البحث عن Arial كبديل ثانٍ
        arial_matches = [
            f for f in available_fonts
            if "arial" in f.lower()
        ]

        if arial_matches:
            cls.FONT_FAMILY = arial_matches[0]
            print(f"تم تعيين الخط: {cls.FONT_FAMILY}")
            return cls.FONT_FAMILY

        # إذا لم تتوفر الخطوط المباشرة، نبحث عن خطوط أخرى بالترتيب
        for font_name in preferred_fonts:
            # البحث عن الخط بطرق مختلفة للتعامل مع تنوع أسماء الخطوط
            if font_name.lower() == "segoe ui light":
                # بحث خاص عن خط Segoe UI Light
                light_matches = [
                    f for f in available_fonts
                    if ("segoe ui light" in f.lower() or
                        "segoe ui" in f.lower() and "light" in f.lower() or
                        "seguil" in f.lower().replace(" ", ""))
                ]
                if light_matches:
                    cls.FONT_FAMILY = light_matches[0]
                    print(f"تم العثور على خط Segoe UI Light: {cls.FONT_FAMILY}")
                    break
            else:
                # البحث العام للخطوط الأخرى
                matching_fonts = [f for f in available_fonts if font_name.lower() in f.lower()]
                if matching_fonts:
                    cls.FONT_FAMILY = matching_fonts[0]
                    print(f"تم تعيين الخط في التنسيقات: {cls.FONT_FAMILY}")
                    break

        return cls.FONT_FAMILY

    # متغير لتخزين السمة الحالية
    CURRENT_THEME = "فاتح"

    # ألوان السمة الفاتحة
    LIGHT_COLORS = {
        # ألوان أساسية
        "primary": "#2563eb",  # أزرق أكثر تشبع
        "primary_hover": "#1d4ed8",
        "primary_pressed": "#1e40af",
        "primary_light": "#3b82f6",

        # ألوان الخلفية
        "background": "#f1f5f9",
        "white": "#ffffff",

        # ألوان الشريط الجانبي
        "sidebar_bg": "#0f172a",
        "sidebar_header_bg": "#020617",
        "sidebar_border": "#1e293b",
        "sidebar_border_hover": "#334155",

        # ألوان النصوص
        "text_dark": "#020617",
        "text_medium": "#1e293b",
        "text_light": "#475569",
        "text_xlight": "#64748b",

        # ألوان أخرى
        "border": "#94a3b8",
        "border_light": "#cbd5e1",
        "hover": "#e2e8f0"
    }

    # ألوان السمة الداكنة
    DARK_COLORS = {
        # ألوان أساسية
        "primary": "#3b82f6",  # أزرق أفتح للسمة الداكنة
        "primary_hover": "#60a5fa",
        "primary_pressed": "#2563eb",
        "primary_light": "#93c5fd",

        # ألوان الخلفية
        "background": "#111827",  # خلفية داكنة
        "white": "#1f2937",      # أبيض داكن

        # ألوان الشريط الجانبي
        "sidebar_bg": "#030712",
        "sidebar_header_bg": "#000000",
        "sidebar_border": "#374151",
        "sidebar_border_hover": "#4b5563",

        # ألوان النصوص
        "text_dark": "#f9fafb",    # نص فاتح
        "text_medium": "#e5e7eb",  # نص متوسط
        "text_light": "#d1d5db",   # نص فاتح
        "text_xlight": "#9ca3af",  # نص فاتح جداً

        # ألوان أخرى
        "border": "#4b5563",
        "border_light": "#374151",
        "hover": "#374151"
    }

    # الألوان الحالية (تبدأ بالسمة الفاتحة)
    COLORS = LIGHT_COLORS.copy()

    @classmethod
    def set_theme(cls, theme_name):
        """تغيير السمة وتحديث الألوان"""
        cls.CURRENT_THEME = theme_name

        if theme_name == "داكن":
            cls.COLORS = cls.DARK_COLORS.copy()
        elif theme_name == "فاتح":
            cls.COLORS = cls.LIGHT_COLORS.copy()
        else:  # نظام التشغيل - افتراضياً فاتح
            cls.COLORS = cls.LIGHT_COLORS.copy()

        print(f"تم تغيير السمة إلى: {theme_name}")

    @classmethod
    def get_current_theme(cls):
        """الحصول على السمة الحالية"""
        return cls.CURRENT_THEME

    # تنسيقات الشريط الجانبي
    SIDEBAR_STYLE = """
        #sidebar {
            background-color: #e0e0e0;
            color: black;
            border: 1px solid #cccccc;
        }
        #title_widget {
            background-color: #f0f8ff;
            padding-top: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #bee3f8;
        }
        #title_label {
            color: #1e3a8a;
            font-size: 16px;
            padding: 5px;
            font-weight: bold;
        }
        #nav_frame {
            background-color: transparent;
            border: none;
        }
        #sidebar_separator, #content_separator {
            color: #bbbbbb;
            background-color: #bbbbbb;
            border: none;
            max-height: 1px;
            border-right: 1px solid rgba(0, 0, 0, 0.1);
            margin: 0;
            padding: 0;
        }
        #footer_label {
            color: #777777;
            font-size: 10px;
            padding: 10px;
        }
        #nav_button {
            background-color: #e8f2ff;
            color: #2c5282;
            border: 1px solid #bee3f8;
            border-radius: 4px;
            text-align: center;
            padding: 10px 15px;
            margin: 3px 0px;
            font-weight: normal;
        }
        #nav_button:hover {
            background-color: #dbeafe;
            border: 1px solid #93c5fd;
            color: #1e40af;
        }
        #nav_button:checked {
            background-color: #bfdbfe;
            color: #1e3a8a;
            font-weight: bold;
            border: 1px solid #60a5fa;
        }
    """ % COLORS

    # تنسيقات الصفحات
    PAGE_STYLE = """
        QWidget {
            background-color: %(white)s;
        }

        #reports_tabs QWidget {
            background-color: transparent;
        }

        #filter_frame QWidget {
            background-color: transparent;
        }

        #page_title {
            color: %(text_dark)s;
            margin-bottom: 5px;
            margin-top: 0px;
            background-color: transparent;
            font-weight: bold;
        }
        QLabel {
            color: %(text_medium)s;
            background-color: transparent;
        }
        #placeholder_content {
            color: %(text_xlight)s;
            font-style: italic;
        }
        #content_separator {
            background-color: %(border_light)s;
            color: %(border_light)s;
            max-height: 1px;
            border-right: 1px solid rgba(0, 0, 0, 0.1);
            margin: 0;
            padding: 0;
        }
        #field_label {
            color: %(text_light)s;
            margin-right: 8px;
            background-color: transparent;
        }
    """ % COLORS

    # تنسيقات الأزرار
    BUTTON_STYLE = """
        /* تنسيق الزر الافتراضي للويندوز - يطبق على جميع الأزرار */
        QPushButton {
            background-color: #e1e1e1;
            color: #000000;
            border: 1px solid #adadad;
            border-radius: 2px;
            padding: 5px 12px;
            text-align: center;
            min-width: 80px;
            min-height: 24px;
        }

        QPushButton:hover {
            background-color: #e5f1fb;
            border: 1px solid #0078d7;
        }

        QPushButton:pressed {
            background-color: #cce4f7;
            border: 1px solid #0078d7;
        }

        QPushButton:focus {
            border: 1px solid #0078d7;
            outline: none;
        }

        QPushButton:default {
            background-color: #0078d7;
            color: white;
            border: 1px solid #0078d7;
        }

        QPushButton:default:hover {
            background-color: #1a86d9;
            border: 1px solid #1a86d9;
        }

        QPushButton:default:pressed {
            background-color: #006cc1;
            border: 1px solid #006cc1;
        }

        QPushButton:disabled {
            background-color: #006cc1;
            color: #666666;
            border: 1px solid #d0d0d0;
        }

        /* تنسيقات الأزرار المخصصة */
        QPushButton#action_button {
            background-color: #0078d7;
            color: white;
            border: 1px solid #0078d7;
            border-radius: 2px;
            padding: 5px 12px;
            text-align: center;
        }

        QPushButton#action_button:hover {
            background-color: #1a86d9;
            border: 1px solid #1a86d9;
        }

        QPushButton#action_button:pressed {
            background-color: #006cc1;
            border: 1px solid #006cc1;
        }

        QPushButton#action_button:disabled {
            background-color: #cccccc;
            color: #666666;
            border: 1px solid #bbbbbb;
        }

        /* أزرار ثانوية */
        QPushButton#secondary_button {
            background-color: #e1e1e1;
            color: #000000;
            border: 1px solid #adadad;
            border-radius: 2px;
            padding: 5px 12px;
            text-align: center;
        }

        QPushButton#secondary_button:hover {
            background-color: #e5f1fb;
            border: 1px solid #0078d7;
        }

        QPushButton#secondary_button:pressed {
            background-color: #cce4f7;
            border: 1px solid #0078d7;
        }

        QPushButton#secondary_button:disabled {
            background-color: #006cc1;
            color: #666666;
            border: 1px solid #d0d0d0;
        }

        /* أزرار المسح والإلغاء */
        QPushButton#danger_button {
            background-color: #e81123;
            color: white;
            border: 1px solid #e81123;
            border-radius: 2px;
            padding: 5px 12px;
            text-align: center;
        }

        QPushButton#danger_button:hover {
            background-color: #f1707a;
            border: 1px solid #f1707a;
        }

        QPushButton#danger_button:pressed {
            background-color: #d0021b;
            border: 1px solid #d0021b;
        }

        QPushButton#danger_button:disabled {
            background-color: #f0a0a0;
            color: #666666;
            border: 1px solid #e0a0a0;
        }

        /* الحفاظ على تنسيق أزرار التنقل */
        QPushButton#nav_button {
            background-color: #e8f2ff;
            color: #2c5282;
            border: 1px solid #bee3f8;
            border-radius: 4px;
            text-align: center;
            padding: 10px 15px;
            margin: 3px 0px;
            font-weight: normal;
        }

        QPushButton#nav_button:hover {
            background-color: #dbeafe;
            border: 1px solid #93c5fd;
            color: #1e40af;
        }

        QPushButton#nav_button:checked {
            background-color: #bfdbfe;
            color: #1e3a8a;
            font-weight: bold;
            border: 1px solid #60a5fa;
        }
    """ % COLORS

    # تنسيقات عناصر الإدخال
    INPUT_STYLE = """
        QLineEdit, QTextEdit, QPlainTextEdit {
            border: 1px solid %(border)s;
            border-radius: 6px;
            padding: 5px 10px;
            background-color: transparent;
        }
        QLineEdit:hover, QTextEdit:hover, QPlainTextEdit:hover {
            border: 1px solid %(text_light)s;
        }
        QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
            border: 1px solid %(primary)s;
            background-color: rgba(255, 255, 255, 0.5);
        }

        QLineEdit#search_input, QLineEdit#date_input {
            background-color: transparent;
        }

        QDateEdit {
            border: 1px solid %(border)s;
            border-radius: 6px;
            padding: 3px 10px;
            background-color: transparent;
        }

        QDateEdit:hover {
            border-color: %(text_light)s;
        }

        QDateEdit:focus {
            border-color: %(primary)s;
            background-color: rgba(255, 255, 255, 0.5);
        }

        #combo_box {
            border: 1px solid %(border)s;
            border-radius: 6px;
            padding: 2px 10px 2px 5px;
            min-height: 22px;
            background-color: transparent;
            selection-background-color: %(primary)s;
            selection-color: white;
            text-align: right;
        }
        #combo_box:hover {
            border: 1px solid %(text_light)s;
        }
        #combo_box:focus {
            border: 1px solid %(primary_light)s;
            background-color: rgba(255, 255, 255, 0.5);
        }
        #combo_box::drop-down {
            subcontrol-origin: padding;
            subcontrol-position: top left;
            width: 16px;
            border-right: none;
            border-top-left-radius: 3px;
            border-bottom-left-radius: 3px;
        }
        #combo_box::down-arrow {
            image: none;
            width: 8px;
            height: 8px;
        }
        #combo_box QAbstractItemView {
            border: 1px solid %(border)s;
            border-radius: 6px;
            background-color: %(white)s;
            selection-background-color: %(primary)s;
            selection-color: white;
        }
    """ % COLORS

    # تنسيقات الجداول
    TABLE_STYLE = """
        QTableView, QTableWidget {
            border: 1px solid %(border_light)s;
            background-color: white;
            gridline-color: %(border_light)s;
            border-radius: 6px;
            selection-background-color: rgba(59, 130, 246, 0.2);
            selection-color: %(text_dark)s;
            outline: none;
        }
        QTableView::item, QTableWidget::item {
            padding: 8px;
            border-bottom: 1px solid %(border_light)s;
            outline: none;
        }
        QTableView::item:selected, QTableWidget::item:selected {
            background-color: rgba(59, 130, 246, 0.2);
            color: %(text_dark)s;
            outline: none;
        }
        QTableView::item:focus, QTableWidget::item:focus {
            outline: none;
        }
        QHeaderView::section {
            background-color: %(hover)s;
            padding: 10px;
            border: none;
            border-bottom: 1px solid %(border)s;
            font-weight: bold;
            color: %(text_medium)s;
        }
        QTableView QHeaderView::section:first, QTableWidget QHeaderView::section:first {
            border-top-right-radius: 6px;
        }
        QTableView QHeaderView::section:last, QTableWidget QHeaderView::section:last {
            border-top-left-radius: 6px;
        }
    """ % COLORS

    # تنسيقات التبويبات
    TABS_STYLE = """
        #settings_tabs, #reports_tabs {
            border: none;
        }
        #settings_tabs::pane, #reports_tabs::pane {
            border: 1px solid #cccccc;
            background: white;
            border-radius: 4px;
            padding: 5px;
        }
        QTabBar::tab {
            background: #f0f0f0;
            border: 1px solid #cccccc;
            border-bottom: none;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
            padding: 8px 16px;
            margin-left: 2px;
            color: #555555;
            min-width: 120px;
            font-weight: normal;
        }
        QTabBar::tab:selected {
            background: white;
            border-bottom: 2px solid white;
            color: #333333;
            font-weight: bold;
            border-top: 2px solid #999999;
            border-left: 1px solid #cccccc;
            border-right: 1px solid #cccccc;
        }
        QTabBar::tab:hover:!selected {
            background: #e5e5e5;
            border: 1px solid #cccccc;
            border-bottom: none;
        }

        /* Estilos específicos para la pestaña de reportes */
        #reports_tabs QTabWidget::pane {
            background-color: transparent;
            margin-top: 10px; /* إضافة هامش علوي للتبويبات الفرعية */
        }

        /* تقليل الهامش العلوي للتابات الفرعية في تاب التقارير */
        #reports_tabs QTabBar {
            margin-top: 8px; /* زيادة الهامش العلوي للتابات من 2px إلى 8px */
        }

        #reports_tabs QTabBar::tab {
            padding-top: 4px; /* تقليل الهامش الداخلي العلوي */
            padding-bottom: 4px; /* تقليل الهامش الداخلي السفلي */
        }
    """ % COLORS

    # تنسيقات الخط العام
    @classmethod
    def get_font_style(cls):
        # Obtener estilos de fuente para la aplicación
        # استرجاع تنسيقات الخط المستخدم في التطبيق
        return f'''
        * {{
            font-family: "{cls.FONT_FAMILY}", Tahoma, Arial;
            font-size: 10pt;
        }}

        /* تحديد الخط بشكل واضح للعناصر المهمة */
        QLabel, QPushButton, QCheckBox, QRadioButton, QComboBox, QLineEdit, QTextEdit, QPlainTextEdit {{
            font-family: "{cls.FONT_FAMILY}", Tahoma, Arial;
        }}

        /* تعيين الاتجاه من اليمين إلى اليسار للعناصر النصية */
        QLineEdit, QTextEdit, QPlainTextEdit, QLabel {{
            text-align: right;
            direction: rtl;
        }}

        /* إزالة الإطار المنقط عند التركيز */
        *:focus {{
            outline: none;
        }}

        QListWidget, QListView {{
            outline: none;
        }}
        QListWidget::item:focus, QListView::item:focus {{
            outline: none;
        }}
        QListWidget::item:selected, QListView::item:selected {{
            outline: none;
        }}
        '''

    # تنسيقات شريط الحالة السفلي
    STATUS_BAR_STYLE = """
        QStatusBar {
            background-color: #e3f2fd;
            color: #1e293b;
            border-top: 1px solid #bbdefb;
            min-height: 28px;
            padding: 3px 12px;
        }
        #status_item {
            color: #475569;
            padding: 0px 12px;
            font-size: 12px;
            font-weight: 500;
        }
        #status_username {
            color: #1e40af;
            font-weight: bold;
            padding: 4px 12px;
            font-size: 12px;
            background-color: #e0f2fe;
            border-radius: 4px;
            border: 1px solid #bae6fd;
        }
        #status_username:hover {
            background-color: #dbeafe;
            border: 1px solid #93c5fd;
            color: #1d4ed8;
        }
        #status_separator {
            color: #94a3b8;
            font-weight: normal;
        }
    """

    # تنسيقات التقارير والفلاتر
    REPORTS_FILTER_STYLE = """
        /* تنسيق قسم الفلترة الجديد */
        #filter_frame {
            background-color: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
        }

        #filter_title {
            color: #1e293b;
            padding: 3px 0;
            font-size: 14px;
            margin-bottom: 0px;
        }

        #filter_separator {
            background-color: #e2e8f0;
            color: #e2e8f0;
            max-height: 1px;
            margin: 0px;
            padding: 0px;
        }

        #field_label {
            color: %(text_medium)s;
            font-weight: bold;
            padding-right: 2px;
            background-color: transparent;
        }

        /* تنسيقات عناصر الإدخال في الفلاتر */
        #date_input {
            border: 1px solid %(border)s;
            border-radius: 6px;
            padding: 5px 10px;
            background-color: transparent;
            min-width: 120px;
        }

        #date_input:hover {
            border-color: %(text_light)s;
        }

        #date_input:focus {
            border-color: %(primary)s;
            outline: none;
            background-color: rgba(255, 255, 255, 0.5);
        }

        #search_input {
            min-width: 180px;
            background-color: transparent;
        }

        #search_input:hover {
            border-color: %(text_light)s;
        }

        #search_input:focus {
            border-color: %(primary)s;
            outline: none;
            background-color: rgba(255, 255, 255, 0.5);
        }

        /* Estilo específico para los widgets QComboBox en la sección de filtro */
        QComboBox#combo_box {
            background-color: transparent;
            border: 1px solid %(border)s;
            border-radius: 6px;
        }

        QComboBox#combo_box:hover {
            border-color: %(text_light)s;
        }

        QComboBox#combo_box:focus {
            border-color: %(primary)s;
            background-color: rgba(255, 255, 255, 0.5);
        }

        QComboBox#combo_box::drop-down {
            subcontrol-origin: padding;
            subcontrol-position: center left;
            width: 18px;
            border: none;
        }

        QComboBox#combo_box::down-arrow {
            width: 10px;
            height: 10px;
        }

        QComboBox#combo_box QAbstractItemView {
            background-color: #f8fafc;
            border: 1px solid %(border)s;
            border-radius: 6px;
            selection-background-color: %(primary)s;
            selection-color: white;
        }

        #action_button {
            /* تم إزالة الهامش العلوي */
        }

        #action_button:hover {
            background-color: %(primary_hover)s;
        }

        #action_button:pressed {
            background-color: %(primary_pressed)s;
        }

        #section_title {
            color: %(text_dark)s;
            margin-top: 10px;
            background-color: transparent;
        }

        #report_date {
            color: %(text_medium)s;
            font-size: 12px;
            padding-right: 10px;
            background-color: transparent;
        }

        /* لون خلفية للصفوف البديلة في الجدول */
        #items_table::item:alternate {
            background-color: #f8fafc;
        }

        /* تنسيق شريط التمرير في الجدول */
        QScrollBar:vertical {
            border: none;
            background: #f1f5f9;
            width: 10px;
            margin: 0px;
            border-radius: 5px;
        }

        QScrollBar::handle:vertical {
            background: #94a3b8;
            min-height: 20px;
            border-radius: 5px;
        }

        QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
            height: 0px;
        }

        QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
            background: none;
        }
    """ % COLORS

    @classmethod
    def get_main_style(cls):
        """دمج جميع التنسيقات وإرجاعها"""
        # تهيئة الخطوط إذا لم يتم تهيئتها بعد
        if cls.FONT_FAMILY == "Arial" and not hasattr(cls, "_fonts_initialized"):
            cls.initialize_fonts()
            cls._fonts_initialized = True

        # تجميع التنسيقات
        all_styles = [
            cls.get_font_style(),
            cls.SIDEBAR_STYLE,
            cls.PAGE_STYLE,
            cls.BUTTON_STYLE,
            cls.INPUT_STYLE,
            cls.TABLE_STYLE,
            cls.TABS_STYLE,
            cls.STATUS_BAR_STYLE,
            cls.get_input_dialog_style(),  # إضافة تنسيقات QInputDialog
            cls.get_global_dialog_style(),  # إضافة تنسيقات شاملة لجميع نوافذ الحوار
            # باقي التنسيقات
        ]
        return "\n\n".join(all_styles)

    @classmethod
    def get_all_view_styles(cls):
        """الحصول على كل التنسيقات للعرض"""
        styles = [
            cls.SIDEBAR_STYLE,
            cls.PAGE_STYLE,
            cls.BUTTON_STYLE,
            cls.INPUT_STYLE,
            cls.TABLE_STYLE,
            cls.TABS_STYLE,
            cls.STATUS_BAR_STYLE,
            cls.REPORTS_FILTER_STYLE,
            cls.CONTEXT_MENU_STYLE,
            cls.get_font_style()
        ]

        return "\n".join(styles)

    @classmethod
    def get_dialog_style(cls):
        """الحصول على تنسيقات نوافذ الحوار"""
        styles = [
            cls.PAGE_STYLE,
            cls.BUTTON_STYLE,
            cls.INPUT_STYLE,
            cls.TABLE_STYLE,
            cls.TABS_STYLE,
            cls.CONTEXT_MENU_STYLE,
            cls.get_font_style()
        ]

        dialog_style = """
            QDialog {
                background-color: #006cc1;
                border: 1px solid #d0d0d0;
            }

            QLabel {
                color: #000000;
                background-color: transparent;
            }

            #dialog_title {
                font-size: 16px;
                font-weight: bold;
                color: #000000;
                margin-bottom: 10px;
            }

            /* تنسيق الزر الافتراضي للويندوز */
            QPushButton {
                background-color: #e1e1e1;
                color: #000000;
                border: 1px solid #adadad;
                border-radius: 2px;
                padding: 5px 12px;
                text-align: center;
                min-width: 80px;
                min-height: 24px;
            }

            QPushButton:hover {
                background-color: #e5f1fb;
                border: 1px solid #0078d7;
            }

            QPushButton:pressed {
                background-color: #cce4f7;
                border: 1px solid #0078d7;
            }

            QPushButton:focus {
                border: 1px solid #0078d7;
                outline: none;
            }

            QPushButton:default {
                background-color: #0078d7;
                color: white;
                border: 1px solid #0078d7;
            }

            QPushButton:default:hover {
                background-color: #1a86d9;
                border: 1px solid #1a86d9;
            }

            QPushButton:default:pressed {
                background-color: #006cc1;
                border: 1px solid #006cc1;
            }

            QPushButton:disabled {
                background-color: #f0f0f0;
                color: #666666;
                border: 1px solid #d0d0d0;
            }

            #small_button {
                background-color: #e1e1e1;
                color: #000000;
                border: 1px solid #adadad;
                border-radius: 2px;
                padding: 5px 10px;
                min-width: 60px;
                min-height: 20px;
            }

            #small_button:hover {
                background-color: #e5f1fb;
                border: 1px solid #0078d7;
            }

            #small_button:pressed {
                background-color: #cce4f7;
                border: 1px solid #0078d7;
            }

            QSpinBox, QDoubleSpinBox {
                border: 1px solid #adadad;
                border-radius: 2px;
                padding: 5px;
                background-color: white;
            }

            QSpinBox:hover, QDoubleSpinBox:hover {
                border-color: #0078d7;
            }

            QSpinBox:focus, QDoubleSpinBox:focus {
                border-color: #0078d7;
            }
        """

        styles.append(dialog_style)
        return "\n".join(styles)

    @classmethod
    def get_input_dialog_style(cls):
        """الحصول على تنسيقات خاصة لنوافذ QInputDialog لضمان ظهور الأزرار بشكل صحيح"""
        return """
            /* تنسيقات خاصة لنوافذ QInputDialog */
            QInputDialog {
                background-color: #f0f0f0;
                border: 1px solid #d0d0d0;
            }

            QInputDialog QLabel {
                color: #000000 !important;
                background-color: transparent;
                font-size: 12px;
            }

            QInputDialog QLineEdit, QInputDialog QSpinBox, QInputDialog QDoubleSpinBox {
                border: 1px solid #adadad;
                border-radius: 2px;
                padding: 5px;
                background-color: white;
                color: #000000 !important;
                font-size: 12px;
            }

            QInputDialog QLineEdit:focus, QInputDialog QSpinBox:focus, QInputDialog QDoubleSpinBox:focus {
                border-color: #0078d7;
            }

            /* تنسيق أزرار QInputDialog - إعادة تعيين كاملة للتأكد من الوضوح */
            QInputDialog QPushButton {
                background-color: #e1e1e1 !important;
                color: #000000 !important;
                border: 1px solid #adadad !important;
                border-radius: 2px;
                padding: 5px 12px;
                text-align: center;
                min-width: 80px;
                min-height: 24px;
                font-size: 12px;
                font-weight: normal;
            }

            QInputDialog QPushButton:hover {
                background-color: #e5f1fb !important;
                border: 1px solid #0078d7 !important;
                color: #000000 !important;
            }

            QInputDialog QPushButton:pressed {
                background-color: #cce4f7 !important;
                border: 1px solid #0078d7 !important;
                color: #000000 !important;
            }

            QInputDialog QPushButton:focus {
                border: 1px solid #0078d7 !important;
                outline: none;
                color: #000000 !important;
            }

            QInputDialog QPushButton:default {
                background-color: #0078d7 !important;
                color: white !important;
                border: 1px solid #0078d7 !important;
                font-weight: bold;
            }

            QInputDialog QPushButton:default:hover {
                background-color: #1a86d9 !important;
                border: 1px solid #1a86d9 !important;
                color: white !important;
            }

            QInputDialog QPushButton:default:pressed {
                background-color: #006cc1 !important;
                border: 1px solid #006cc1 !important;
                color: white !important;
            }

            QInputDialog QPushButton:disabled {
                background-color: #f0f0f0 !important;
                color: #666666 !important;
                border: 1px solid #d0d0d0 !important;
            }

            /* تنسيقات إضافية لضمان عدم تأثر الأزرار بتنسيقات أخرى */
            QInputDialog QDialogButtonBox QPushButton {
                background-color: #e1e1e1 !important;
                color: #000000 !important;
                border: 1px solid #adadad !important;
                border-radius: 2px;
                padding: 5px 12px;
                text-align: center;
                min-width: 80px;
                min-height: 24px;
                font-size: 12px;
                font-weight: normal;
            }

            QInputDialog QDialogButtonBox QPushButton:hover {
                background-color: #e5f1fb !important;
                border: 1px solid #0078d7 !important;
                color: #000000 !important;
            }

            QInputDialog QDialogButtonBox QPushButton:pressed {
                background-color: #cce4f7 !important;
                border: 1px solid #0078d7 !important;
                color: #000000 !important;
            }

            QInputDialog QDialogButtonBox QPushButton:default {
                background-color: #0078d7 !important;
                color: white !important;
                border: 1px solid #0078d7 !important;
                font-weight: bold;
            }

            QInputDialog QDialogButtonBox QPushButton:default:hover {
                background-color: #1a86d9 !important;
                border: 1px solid #1a86d9 !important;
                color: white !important;
            }

            QInputDialog QDialogButtonBox QPushButton:default:pressed {
                background-color: #006cc1 !important;
                border: 1px solid #006cc1 !important;
                color: white !important;
            }
        """

    @classmethod
    def get_global_dialog_style(cls):
        """الحصول على تنسيقات شاملة لجميع نوافذ الحوار في Qt لضمان عدم وجود تعارض"""
        return """
            /* تنسيقات شاملة لجميع نوافذ Qt لضمان عدم التعارض */
            QMessageBox {
                background-color: #f0f0f0;
                border: 1px solid #d0d0d0;
            }

            QMessageBox QLabel {
                color: #000000 !important;
                background-color: transparent;
                font-size: 12px;
            }

            /* تنسيقات أزرار QMessageBox */
            QMessageBox QPushButton {
                background-color: #e1e1e1 !important;
                color: #000000 !important;
                border: 1px solid #adadad !important;
                border-radius: 2px;
                padding: 5px 12px;
                text-align: center;
                min-width: 80px;
                min-height: 24px;
                font-size: 12px;
                font-weight: normal;
            }

            QMessageBox QPushButton:hover {
                background-color: #e5f1fb !important;
                border: 1px solid #0078d7 !important;
                color: #000000 !important;
            }

            QMessageBox QPushButton:pressed {
                background-color: #cce4f7 !important;
                border: 1px solid #0078d7 !important;
                color: #000000 !important;
            }

            QMessageBox QPushButton:focus {
                border: 1px solid #0078d7 !important;
                outline: none;
                color: #000000 !important;
            }

            QMessageBox QPushButton:default {
                background-color: #0078d7 !important;
                color: white !important;
                border: 1px solid #0078d7 !important;
                font-weight: bold;
            }

            QMessageBox QPushButton:default:hover {
                background-color: #1a86d9 !important;
                border: 1px solid #1a86d9 !important;
                color: white !important;
            }

            QMessageBox QPushButton:default:pressed {
                background-color: #006cc1 !important;
                border: 1px solid #006cc1 !important;
                color: white !important;
            }

            QMessageBox QPushButton:disabled {
                background-color: #f0f0f0 !important;
                color: #666666 !important;
                border: 1px solid #d0d0d0 !important;
            }

            /* تنسيقات أزرار QDialogButtonBox في جميع النوافذ */
            QDialogButtonBox QPushButton {
                background-color: #e1e1e1 !important;
                color: #000000 !important;
                border: 1px solid #adadad !important;
                border-radius: 2px;
                padding: 5px 12px;
                text-align: center;
                min-width: 80px;
                min-height: 24px;
                font-size: 12px;
                font-weight: normal;
            }

            QDialogButtonBox QPushButton:hover {
                background-color: #e5f1fb !important;
                border: 1px solid #0078d7 !important;
                color: #000000 !important;
            }

            QDialogButtonBox QPushButton:pressed {
                background-color: #cce4f7 !important;
                border: 1px solid #0078d7 !important;
                color: #000000 !important;
            }

            QDialogButtonBox QPushButton:focus {
                border: 1px solid #0078d7 !important;
                outline: none;
                color: #000000 !important;
            }

            QDialogButtonBox QPushButton:default {
                background-color: #0078d7 !important;
                color: white !important;
                border: 1px solid #0078d7 !important;
                font-weight: bold;
            }

            QDialogButtonBox QPushButton:default:hover {
                background-color: #1a86d9 !important;
                border: 1px solid #1a86d9 !important;
                color: white !important;
            }

            QDialogButtonBox QPushButton:default:pressed {
                background-color: #006cc1 !important;
                border: 1px solid #006cc1 !important;
                color: white !important;
            }

            QDialogButtonBox QPushButton:disabled {
                background-color: #f0f0f0 !important;
                color: #666666 !important;
                border: 1px solid #d0d0d0 !important;
            }
        """

    @classmethod
    def get_system_font(cls, size=10, bold=False, italic=False):
        """
        الحصول على كائن QFont مهيأ بالخط النظامي الذي تم اختياره مع التحكم في الحجم والنمط

        Args:
            size (int): حجم الخط
            bold (bool): تمكين نمط عريض
            italic (bool): تمكين نمط مائل

        Returns:
            QFont: كائن خط مهيأ بالإعدادات المطلوبة
        """
        # تهيئة الخطوط إذا لم يتم تهيئتها بعد
        if cls.FONT_FAMILY == "Arial" and not hasattr(cls, "_fonts_initialized"):
            cls.initialize_fonts()
            cls._fonts_initialized = True

        font = QFont(cls.FONT_FAMILY, size)
        if bold:
            font.setBold(True)
        if italic:
            font.setItalic(True)
        return font

    # تنسيقات قائمة السياق (QMenu)
    CONTEXT_MENU_STYLE = """
        QMenu {
            background-color: white;
            border: 1px solid #cbd5e1;
            border-radius: 6px;
            padding: 5px;
            color: #334155; /* لون النص الافتراضي */
        }

        QMenu::item {
            padding: 8px 25px;
            border-radius: 4px;
            color: #334155; /* لون النص العادي */
        }

        QMenu::item:selected {
            background-color: #3b82f6;
            color: white; /* لون أبيض للنص عند المرور عليه */
        }

        QMenu::item:disabled {
            color: #9ca3af; /* لون رمادي للعناصر المعطلة */
            background-color: transparent;
        }

        QMenu::separator {
            height: 1px;
            background-color: #e2e8f0;
            margin: 5px 0px;
        }
    """

    @classmethod
    def get_context_menu_style(cls):
        """الحصول على تنسيقات قوائم السياق"""
        return cls.CONTEXT_MENU_STYLE

    @classmethod
    def get_login_style(cls):
        """الحصول على تنسيقات صفحة تسجيل الدخول"""

        # تهيئة الخطوط إذا لم يتم تهيئتها بعد
        if cls.FONT_FAMILY == "Arial" and not hasattr(cls, "_fonts_initialized"):
            cls.initialize_fonts()
            cls._fonts_initialized = True

        # تنسيقات صفحة تسجيل الدخول
        login_style = f"""
            QDialog {{
                background-color: #f8fafc;
            }}

            #login_input {{
                border: 1px solid {cls.COLORS['border']};
                border-radius: 6px;
                padding: 8px 12px;
                background-color: white;
                font-size: 14px;
                font-family: "{cls.FONT_FAMILY}", Tahoma, Arial;
                text-align: center;
                direction: rtl;
            }}

            QComboBox#login_input {{
                padding: 5px 10px;
                background-color: white;
                font-size: 14px;
                text-align: center;
            }}

            QComboBox#login_input::drop-down {{
                subcontrol-origin: padding;
                subcontrol-position: left top;
                width: 20px; /* إعادة ظهور زر القائمة المنسدلة */
                background-color: transparent;
                border: none;
            }}

            QComboBox#login_input::down-arrow {{
                width: 14px;
                height: 14px;
                background-color: transparent;
                color: transparent;
                image: none; /* إزالة صورة السهم */
            }}

            QComboBox#login_input QAbstractItemView {{
                border: 1px solid {cls.COLORS['border']};
                border-radius: 6px;
                background-color: white;
                selection-background-color: {cls.COLORS['primary']};
                selection-color: white;
            }}

            #login_input:focus {{
                border: 1px solid {cls.COLORS['primary']};
            }}

            /* تنسيق الزر الافتراضي للويندوز */
            QPushButton {{
                background-color: #e1e1e1;
                color: #000000;
                border: 1px solid #adadad;
                border-radius: 2px;
                padding: 5px 12px;
                text-align: center;
                min-width: 80px;
                min-height: 24px;
                font-size: 14px;
            }}

            QPushButton:hover {{
                background-color: #e5f1fb;
                border: 1px solid #0078d7;
            }}

            QPushButton:pressed {{
                background-color: #cce4f7;
                border: 1px solid #0078d7;
            }}

            QPushButton:focus {{
                border: 1px solid #0078d7;
                outline: none;
            }}

            QPushButton:default {{
                background-color: #0078d7;
                color: white;
                border: 1px solid #0078d7;
            }}

            QPushButton:default:hover {{
                background-color: #1a86d9;
                border: 1px solid #1a86d9;
            }}

            QPushButton:default:pressed {{
                background-color: #006cc1;
                border: 1px solid #006cc1;
            }}

            QPushButton:disabled {{
                background-color: #f0f0f0;
                color: #666666;
                border: 1px solid #d0d0d0;
            }}

            /* أزرار خاصة */
            QPushButton#login_button {{
                background-color: #0078d7;
                color: white;
                border: 1px solid #0078d7;
                border-radius: 2px;
                padding: 8px 16px;
                font-size: 14px;
                font-weight: bold;
            }}

            QPushButton#login_button:hover {{
                background-color: #1a86d9;
                border: 1px solid #1a86d9;
            }}

            QPushButton#login_button:pressed {{
                background-color: #006cc1;
                border: 1px solid #006cc1;
            }}

            QPushButton#exit_button {{
                background-color: #e1e1e1;
                color: #000000;
                border: 1px solid #adadad;
                border-radius: 2px;
                padding: 8px 16px;
                font-size: 14px;
            }}

            QPushButton#exit_button:hover {{
                background-color: #e5f1fb;
                border: 1px solid #0078d7;
            }}

            QPushButton#exit_button:pressed {{
                background-color: #cce4f7;
                border: 1px solid #0078d7;
            }}

            #login_label {{
                font-size: 14px;
                font-weight: bold;
                color: {cls.COLORS['text_medium']};
                font-family: "{cls.FONT_FAMILY}", Tahoma, Arial;
            }}

            #copyright_label {{
                color: {cls.COLORS['text_light']};
                font-size: 12px;
                margin-top: 20px;
            }}

            #separator {{
                color: {cls.COLORS['border']};
                margin: 10px 0;
            }}

            #remember_checkbox {{
                font-size: 14px;
            }}


        """

        # دمج تنسيقات الخط مع تنسيقات صفحة تسجيل الدخول
        return cls.get_font_style() + login_style
